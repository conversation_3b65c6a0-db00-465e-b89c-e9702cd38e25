<template lang="pug">
div(:id="`${item.uid}_wrapper`" ref="containerRef" :class="{ ...classObj, 'om-full': !isTeaser }")
  EditorContent(
    v-if="isSelected"
    key="editor"
    :id="item.uid"
    :editor="editor"
    :class="elementClass"
    :data-event-settings="btnEventSettings"
    :data-success-copy-text="successCopyText"
    :data-failure-copy-text="failureCopyText"
  )
  div(
    v-else=""
    :id="item.uid"
    key="display"
    :class="elementClass"
    v-html="content"
    :data-event-settings="btnEventSettings"
    :data-success-copy-text="successCopyText"
    :data-failure-copy-text="failureCopyText"
  )
</template>

<script>
  import { debounce } from 'lodash-es';
  import { mapState, mapActions, mapMutations } from 'vuex';
  import itemMixin from '@/editor/mixins/item';
  import { SET_ACTIVE_ELEMENT, SET_VISIBLE } from '@/store/wysiwyg';
  import { EditorContent, Editor } from '@tiptap/vue-2';
  import Text from '@tiptap/extension-text';

  import { OmDocument } from './extensions/Document';
  import { OmParagraph } from './extensions/Paragraph';
  import { OmTextAlign } from './extensions/TextAlign';
  import { DynamicText } from './extensions/DynamicText';
  import { TextStyle } from './extensions/TextStyle';
  import { ApplyStyleToEmptyLines } from './extensions/ApplyStyleToEmptyLines';
  // import { OmListItem } from './extensions/ListItem';
  // import { OmBulletList } from './extensions/BulletList';
  // import { OmOrderedList } from './extensions/OrderedList';
  import { getStyles } from './helpers/getStyles';

  function getFormatting(editor, forced = false) {
    const { state } = editor ?? {};
    const { from, to } = state.selection ?? {};
    const docLength = state?.doc?.content?.size ?? null;

    if (forced || (from === 0 && to === docLength)) {
      const styles = getStyles();
      // always reset linkAttrs
      styles.linkAttrs = null;

      return Object.keys(styles).length === 10 ? styles : null;
    }

    return null;
  }

  function buildEditor(content, editable = false, themeKit = null) {
    return new Editor({
      editable,
      extensions: [
        OmDocument,
        OmParagraph,
        Text,
        OmTextAlign.configure({ defaultAlignment: 'center' }),
        DynamicText,
        TextStyle.configure({
          types: ['textStyle', 'link'],
          mergeNestedSpanStyles: true,
          defaultFont: themeKit ? 'om-font-1' : 'open-sans',
        }),
        ApplyStyleToEmptyLines,
        // OmBulletList.configure({
        //   keepMarks: true,
        //   keepAttributes: true,
        // }),
        // OmOrderedList.configure({
        //   keepMarks: true,
        //   keepAttributes: true,
        // }),
        // OmListItem,
      ],
      content,
    });
  }

  export default {
    components: { EditorContent },
    mixins: [itemMixin],
    props: {
      elementClass: String,
      item: Object,
      extraClass: String,
    },
    data: () => ({
      editor: null,
      textStyle: null,
      resizeObserver: null,
      copiedStyle: null,
      isSelectAll: false,
      justPressedEnterInLink: false,
      stylesAfterEnter: null,
    }),
    computed: {
      ...mapState([
        'template',
        'selectedElement',
        'docker',
        'mobilePreview',
        'lastInsertedElementId',
      ]),
      ...mapState('wysiwyg', ['activeElement']),

      isButton() {
        return this.item.type.includes('Button');
      },
      isText() {
        return this.item.type.includes('Text');
      },
      btnEventSettings() {
        return this.isButton
          ? JSON.stringify({
              action: this.item.data.action,
              url: this.item.data.redirectUrl,
              phoneNumber: this.item.data.phoneNumber,
              newTab: this.item.data.newTab,
              keepQueryParams: this.item.data.keepQueryParams,
              jumpTo: +this.item.data.jumpToPage + 1,
              reportAs: this.item.data.reportAs,
              isFilled: this.item.data.status,
              syncToIntegration: this.item.data.syncToIntegration,
            })
          : null;
      },
      successCopyText() {
        return this.item.data?.coupon?.successCopyText;
      },
      failureCopyText() {
        return this.item.data?.coupon?.failureCopyText;
      },
      isTeaser() {
        return this.item.type.includes('Teaser');
      },
      content() {
        const result = this.item.data.text || this.$t('typeHere');
        return result.replace(/<span class="ql-cursor"><\/span>/g, '');
      },
      classObj() {
        const result = {};
        let type;
        if (this.isButton) {
          type = 'button';
        } else if (this.isText) {
          type = 'text';
        }
        if (this.extraClass === null) {
          const typeStyle = this.template.style[type];
          if (typeStyle && typeStyle.textJustify) result[typeStyle.textJustify] = true;
        } else if (Array.isArray(this.extraClass)) {
          this.extraClass.forEach((extraClass) => {
            result[extraClass] = true;
          });
        } else if (typeof this.extraClass !== 'undefined') {
          result[this.extraClass] = true;
        }
        return result;
      },
      isSelected() {
        return !!(
          (this.selectedElement && this.selectedElement.uid === this.item.uid) ||
          this.item.selected
        );
      },
      isHidden() {
        const device = this.mobilePreview ? 'mobile' : 'desktop';
        return this.selectedElement?.[device]?.hidden ?? false;
      },
    },

    watch: {
      selectedElement(selectedElement) {
        if (!selectedElement) {
          this.updateWysiwyg({ visible: false, uid: null });
        }
      },
      isSelected: {
        handler(selected) {
          if (!selected) {
            this.editor?.destroy?.();
            if (this.resizeObserver) {
              this.resizeObserver.disconnect();
            }
            this.editor = null;
            this.savedStyles = null;
            this.isSelectAll = false;
            this.justPressedEnterInLink = false;
            this.stylesAfterEnter = null;
            return;
          }

          this.initialize();

          this.$nextTick(() => {
            this.applyStyles(this.copiedStyle);
            this.$bus.$emit('resetCopiedStyle');
          });
        },
        immediate: true,
      },

      'editor.view.dom': {
        handler() {
          this.$nextTick(() => {
            this.initResizeObserver();
          });
        },
      },
      'editor.isEmpty': {
        handler(isEmpty, prevIsEmpty) {
          if (!isEmpty && prevIsEmpty) {
            this.editor
              .chain()
              .focus('all')
              .setMark('textStyle', this.textStyle)
              .selectTextblockEnd()
              .focus('end')
              .run();
          }
        },
      },
      isHidden(hidden) {
        if (hidden) this.hideWysiwyg();
        this.editor?.destroy?.();
        if (!hidden) this.initialize();
      },
    },

    mounted() {
      this.initResizeObserver();
      this.$bus.$on('repositionWysiwyg', this.updateSelectedElementPosition);
      this.$bus.$on('refreshWysiwyg', this.refresh);
      this.$bus.$on('applyCopyStyle', (styles) => {
        this.copiedStyle = styles;
      });
      this.$bus.$on('resetCopiedStyle', () => {
        this.copiedStyle = null;
      });
    },
    beforeDestroy() {
      if (this.resizeObserver) this.resizeObserver.disconnect();
      this.resizeObserver = null;
      this.$bus.$off('repositionWysiwyg', this.updateSelectedElementPosition);
      this.$bus.$off('refreshWysiwyg', this.refresh);
      this.$bus.$off('applyCopyStyle');
      this.$bus.$off('resetCopiedStyle');
    },
    methods: {
      ...mapMutations('wysiwyg', [SET_ACTIVE_ELEMENT, SET_VISIBLE]),
      ...mapActions('wysiwyg', ['refreshPosition', 'showWysiwyg', 'updateWysiwyg', 'hideWysiwyg']),
      debouncedDataUpdate: debounce(function ({ editor }) {
        this.textStyle = getFormatting(editor, true) ?? this.textStyle;
        this.setValueOf('data.text', editor.getHTML());
      }, 150),
      applyStyles(styles) {
        this.editor.commands?.focus?.('all');
        this.editor.commands?.updateAttributes('textStyle', styles);
      },
      updateSelectedElementPosition({ rect, containerScrollTop = 0 } = {}) {
        if (this.item?.uid !== this.activeElement) {
          return;
        }
        const element = document.querySelector(`#${this.item?.uid}`);

        if (!element) return null;

        const workspaceIframe = window.parent.document.querySelector(
          '.om-workspace-container iframe',
        );
        const workspaceIframeRect = workspaceIframe.getBoundingClientRect();

        const elementRect = rect ?? element?.getBoundingClientRect?.();
        const scale = workspaceIframeRect.width / workspaceIframe.offsetWidth;
        const elementTop = (elementRect.top ?? 0) * scale;
        const elementLeft = (elementRect.left ?? 0) * scale;
        const elementWidth = (elementRect.width ?? 0) * scale;

        const conScrollTop =
          (containerScrollTop ??
            window.parent.document.querySelector('.om-workspace-container')?.scrollTop ??
            0) * 2;

        const top = elementTop - workspaceIframeRect.top - conScrollTop;
        const left = workspaceIframeRect.left + elementLeft + elementWidth / 2;

        this.refreshPosition({ top, left });
      },
      initResizeObserver() {
        if (this.resizeObserver) {
          this.resizeObserver.disconnect();
        }

        this.resizeObserver = new ResizeObserver(() => {
          this.updateSelectedElementPosition();
        });

        const element = document.querySelector(`#${this.item?.uid}`);
        if (element) {
          this.resizeObserver.observe(element);
        }
      },
      initialize(previewChange = false) {
        if (!this.isSelected || this.isHidden) return;

        if (previewChange) {
          const uid = this.item.uid;
          this.$store.commit('deselectAll');
          this.$nextTick(() => {
            setTimeout(() => {
              this.$store.commit('selectElementByUid', uid);
            }, 200);
          });
          return;
        }

        this[SET_VISIBLE](this.selectedElement && !this.isHidden); // TODO: is it okay?
        this[SET_ACTIVE_ELEMENT](this.item.uid);
        this.editor = buildEditor(this.item.data.text, !this.docker, this.template.themeKit);
        this.editor.on('update', this.debouncedDataUpdate);
        this.editor.on('selectionUpdate', (event) => {
          this.$bus?.$emit?.('wysiwygUpdate', event);
          this.handleSelectionUpdate(event);
        });

        // Listen for text input after Enter in link
        this.editor.on('update', ({ editor }) => {
          if (this.justPressedEnterInLink && this.stylesAfterEnter) {
            // Apply the saved styles without link
            editor
              .chain()
              .focus()
              .unsetMark('textStyle')
              .setMark('textStyle', this.stylesAfterEnter)
              .run();

            this.justPressedEnterInLink = false;
            this.stylesAfterEnter = null;
          }
        });

        // Add keyboard shortcuts for undo/redo when editor is focused
        this.editor.view.dom.addEventListener('keydown', (event) => {
          if ((event.metaKey || event.ctrlKey) && event.key === 'z') {
            event.preventDefault();
            event.stopPropagation();
            if (event.shiftKey) {
              // Cmd+Shift+Z or Ctrl+Shift+Z for redo
              this.$bus.$emit('historyRedo');
            } else {
              // Cmd+Z or Ctrl+Z for undo
              this.$bus.$emit('historyUndo');
            }
          }

          // Handle Enter key to break out of link formatting
          if (event.key === 'Enter') {
            const currentStyles = this.editor.getAttributes('textStyle') ?? {};
            if (currentStyles.linkAttrs?.href) {
              // Mark that we just pressed Enter in a link
              this.justPressedEnterInLink = true;

              // Save styles without link for later application
              this.stylesAfterEnter = { ...currentStyles };
              delete this.stylesAfterEnter.linkAttrs;

              // Clear the flag after a short delay
              setTimeout(() => {
                this.justPressedEnterInLink = false;
                this.stylesAfterEnter = null;
              }, 200);
            }

            this.editor.commands.applyStyleToEmptyLines(this.textStyle);
          }
        });

        this.updateSelectedElementPosition();

        if (window.top !== window.self) {
          window.top._editor = this.editor;
        }

        if (!this.editor.isEmpty) {
          this.textStyle = getFormatting(this.editor, true) ?? this.textStyle;
        }

        if (this.lastInsertedElementId === this.item.uid) {
          this.setValueOf('data.text', this.editor.getHTML());
        }

        this.editor?.commands?.focus?.('all');
      },
      refresh(uid) {
        if (this.item.uid !== uid) return;
        this.editor?.commands?.setContent?.(this.content, true);
        this.editor?.commands?.focus?.('all');
      },
      handleSelectionUpdate({ editor }) {
        const { from, to } = editor.state.selection;
        const isCurrentlySelectAll = from === 0 && to === editor.state.doc.content.size;

        if (isCurrentlySelectAll && !this.isSelectAll) {
          // Just became select all - save current styles
          this.textStyle = getFormatting(event.editor) ?? this.textStyle;
        } else if (!isCurrentlySelectAll && this.isSelectAll && this.textStyle) {
          // Just left select all state - apply saved styles if content changed
          this.$nextTick(() => {
            if (editor.state.doc.content.size < 2) {
              editor
                .chain()
                .focus('all')
                .setMark('textStyle', this.savedStyles)
                .selectTextblockEnd()
                .focus('end')
                .run();
            }
          });
        }

        this.isSelectAll = isCurrentlySelectAll;
      },
    },
  };
</script>

<style lang="sass">
  .tiptap[contenteditable]
    outline: 0px solid transparent
    white-space: pre-wrap
  .om-dtr-content
    a
      pointer-events: none
</style>
