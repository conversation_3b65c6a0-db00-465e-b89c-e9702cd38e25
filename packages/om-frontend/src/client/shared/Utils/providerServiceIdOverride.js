import { purifyDomain } from './domain';

/**
 * Applies provider service ID override if configured in OptiMonkRegistry
 * @param {string} originalProviderServiceId - The original provider service ID
 * @returns {string} The overridden provider service ID or the original if no override exists
 */
export function applyProviderServiceIdOverride(originalProviderServiceId) {
  // Check for provider service ID override
  if (OptiMonkRegistry && OptiMonkRegistry.providerServiceIdOverrides) {
    const domain = window.location.hostname;
    const pureDomain = purifyDomain(domain);
    const override =
      OptiMonkRegistry.providerServiceIdOverrides[pureDomain] ||
      OptiMonkRegistry.providerServiceIdOverrides[domain];

    if (override) {
      return override;
    }
  }

  return originalProviderServiceId;
}
